import cv2 as cv
import os
from skimage.measure import compare_ssim


path = r'C:\Users\<USER>\PycharmProjects\ProcessVideo\sourcevideo'
video_name = os.listdir(path)
for i in video_name:
    # print(i)
    video_name = i.split('.')[0]
    sourceFileName = video_name
    print(sourceFileName)

    video_path = os.path.join(path, sourceFileName + '.mp4')
    times = 0
    i = 0

    # outputDir = 'video/' + 'new' + '/'
    outputDir = 'imgSec/' + sourceFileName + '/'
    if not os.path.exists(outputDir):
        os.makedirs(outputDir)

    camera = cv.VideoCapture(video_path)
    fps = camera.get(cv.CAP_PROP_FPS)
    frameFrequency = int(fps)

    while True:
        times += 1
        res, image = camera.read()
        tempimage = image
        if times == 1:
            previousframe = cv.cvtColor(tempimage, cv.COLOR_BGR2GRAY)
        try:

            if times % frameFrequency == 0:
                i += 1
                currentframe = cv.cvtColor(tempimage, cv.COLOR_BGR2GRAY)
                score, diff = compare_ssim(previousframe, currentframe, full=True)
                print("times:{},ssim:{}".format(times, score))
                if score < 0.9:
                    cv.imwrite(outputDir + str(i) + '.jpg', tempimage)
                    print(outputDir + str(i) + '.jpg')
                    previousframe = cv.cvtColor(tempimage, cv.COLOR_BGR2GRAY)

                # previousframe = cv.cvtColor(tempimage, cv.COLOR_BGR2GRAY)
                # cv.imwrite(outputDir + str(i) + '.jpg', image)
                # print(outputDir + str(i) + '.jpg')
        except Exception as e:
            pass
        if not res:
            print('not res , not image')
            break
    print('图片提取结束')

print('process video complete')
camera.release()
