import cv2 as cv
import os
from skimage.metrics import structural_similarity as compare_ssim


path = r'C:\Users\<USER>\PycharmProjects\workingproject\ProcessVideo\sourcevideo'
video_name = os.listdir(path)
for i in video_name:
    # print(i)
    video_name = i.split('.')[0]
    sourceFileName = video_name

    video_path = os.path.join(path, sourceFileName + '.mp4')
    times = 0
    i = 0

    # outputDir = 'video/' + 'new' + '/'
    outputDir = 'img/' + sourceFileName + '/'
    if not os.path.exists(outputDir):
        os.makedirs(outputDir)

    camera = cv.VideoCapture(video_path)
    fps = camera.get(cv.CAP_PROP_FPS)
    frameFrequency = int(fps)

    while True:
        times += 1
        res, image = camera.read()
        if res:
            tempimage = image
            if times == 1:
                cv.imwrite(outputDir + str(times) + '.jpg', image)
                previousframe = cv.cvtColor(tempimage, cv.COLOR_BGR2GRAY)

            if times >= 2:
                currentframe = cv.cvtColor(tempimage, cv.COLOR_BGR2GRAY)
                score, diff = compare_ssim(previousframe, currentframe, full=True)
                print("ssim:{}".format(score))
                if score < 0.9:
                    cv.imwrite(outputDir + str(times) + '.jpg', tempimage)
                    print(outputDir + str(times) + '.jpg')
                    previousframe = cv.cvtColor(tempimage, cv.COLOR_BGR2GRAY)
        else:
            break

    # while True:
    #     times += 1
    #     res, image = camera.read()
    #     if not res:
    #         print('not res , not image')
    #         break
    #     if times % frameFrequency == 0:
    #         i += 1
    #         cv.imwrite(outputDir + str(i) + '.jpg', image)
    #         print(outputDir + str(i) + '.jpg')
    # print('图片提取结束')


print('process video complete')
camera.release()
