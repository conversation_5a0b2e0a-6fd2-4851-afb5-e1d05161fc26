import os
import cv2 as cv
import shutil

img_path = r'C:\Users\<USER>\PycharmProjects\ProcessVideo\imgSec'
save_path = r'C:\Users\<USER>\PycharmProjects\ProcessVideo\img'

files = os.listdir(img_path)
if not os.path.exists(save_path):
    os.mkdir(save_path)

for fi in files:
    # print(fi)
    filedir = os.path.join(img_path, fi)
    filename = os.listdir(filedir)
    for finame in filename:
        # print(filename)
        fullfilename = os.path.join(filedir, finame)
        # shutil.copy(fullfilename, save_path)
        shutil.copyfile(fullfilename, os.path.join(save_path, fi+finame))
        # print(fullfilename)

